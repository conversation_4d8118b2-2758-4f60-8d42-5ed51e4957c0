Requirement already satisfied: twine in d:\software\python\python310\lib\site-packages (6.1.0)
Requirement already satisfied: readme-renderer>=35.0 in d:\software\python\python310\lib\site-packages (from twine) (44.0)
Requirement already satisfied: requests>=2.20 in d:\software\python\python310\lib\site-packages (from twine) (2.31.0)
Requirement already satisfied: requests-toolbelt!=0.9.0,>=0.8.0 in d:\software\python\python310\lib\site-packages (from twine) (1.0.0)
Requirement already satisfied: urllib3>=1.26.0 in d:\software\python\python310\lib\site-packages (from twine) (1.26.15)
Requirement already satisfied: keyring>=15.1 in d:\software\python\python310\lib\site-packages (from twine) (25.6.0)
Requirement already satisfied: rfc3986>=1.4.0 in d:\software\python\python310\lib\site-packages (from twine) (2.0.0)
Requirement already satisfied: rich>=12.0.0 in d:\software\python\python310\lib\site-packages (from twine) (13.3.5)
Requirement already satisfied: packaging>=24.0 in d:\software\python\python310\lib\site-packages (from twine) (25.0)
Requirement already satisfied: id in d:\software\python\python310\lib\site-packages (from twine) (1.5.0)
Requirement already satisfied: pywin32-ctypes>=0.2.0 in d:\software\python\python310\lib\site-packages (from keyring>=15.1->twine) (0.2.3)
Requirement already satisfied: importlib_metadata>=4.11.4 in d:\software\python\python310\lib\site-packages (from keyring>=15.1->twine) (6.6.0)
Requirement already satisfied: jaraco.classes in d:\software\python\python310\lib\site-packages (from keyring>=15.1->twine) (3.4.0)
Requirement already satisfied: jaraco.functools in d:\software\python\python310\lib\site-packages (from keyring>=15.1->twine) (4.2.1)
Requirement already satisfied: jaraco.context in d:\software\python\python310\lib\site-packages (from keyring>=15.1->twine) (6.0.1)
Requirement already satisfied: zipp>=0.5 in d:\software\python\python310\lib\site-packages (from importlib_metadata>=4.11.4->keyring>=15.1->twine) (3.15.0)
Requirement already satisfied: nh3>=0.2.14 in d:\software\python\python310\lib\site-packages (from readme-renderer>=35.0->twine) (0.3.0)
Requirement already satisfied: docutils>=0.21.2 in d:\software\python\python310\lib\site-packages (from readme-renderer>=35.0->twine) (0.21.2)
Requirement already satisfied: Pygments>=2.5.1 in d:\software\python\python310\lib\site-packages (from readme-renderer>=35.0->twine) (2.15.1)
Requirement already satisfied: charset-normalizer<4,>=2 in d:\software\python\python310\lib\site-packages (from requests>=2.20->twine) (3.1.0)
Requirement already satisfied: idna<4,>=2.5 in d:\software\python\python310\lib\site-packages (from requests>=2.20->twine) (3.4)
Requirement already satisfied: certifi>=2017.4.17 in d:\software\python\python310\lib\site-packages (from requests>=2.20->twine) (2022.12.7)
Requirement already satisfied: markdown-it-py<3.0.0,>=2.2.0 in d:\software\python\python310\lib\site-packages (from rich>=12.0.0->twine) (2.2.0)
Requirement already satisfied: mdurl~=0.1 in d:\software\python\python310\lib\site-packages (from markdown-it-py<3.0.0,>=2.2.0->rich>=12.0.0->twine) (0.1.2)
Requirement already satisfied: more-itertools in d:\software\python\python310\lib\site-packages (from jaraco.classes->keyring>=15.1->twine) (10.7.0)
Requirement already satisfied: backports.tarfile in d:\software\python\python310\lib\site-packages (from jaraco.context->keyring>=15.1->twine) (1.2.0)
