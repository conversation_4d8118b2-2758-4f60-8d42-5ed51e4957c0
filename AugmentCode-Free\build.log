2025-07-27 18:29:14,304 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:29:14,304 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:29:14,305 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:29:14,305 - INFO - INFO: Python: 3.10.8
2025-07-27 18:29:14,305 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:29:14,305 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:29:14,307 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:29:14,307 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:29:14,307 - INFO - INFO: Architecture: AMD64
2025-07-27 18:29:14,308 - INFO - SUCCESS: All required files present
2025-07-27 18:29:14,308 - INFO - SUCCESS: Core module directory found
2025-07-27 18:29:14,309 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:29:14,309 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:29:14,309 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.06s)
================================================================================
2025-07-27 18:29:14,316 - INFO - SUCCESS: Removed directory: build
2025-07-27 18:29:14,318 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:29:14,350 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\gui_qt6\__pycache__
2025-07-27 18:29:14,351 - INFO - SUCCESS: Cleanup completed: 3 items removed
2025-07-27 18:29:14,351 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:29:14,351 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:29:14,351 - INFO - INFO: Setting up build directories
2025-07-27 18:29:14,352 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:29:14,352 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:29:14,353 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:29:14,353 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.10s)
================================================================================
2025-07-27 18:29:14,353 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:29:18,585 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:29:18,585 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:18,586 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:29:18,586 - INFO - INFO: Installing core dependencies
2025-07-27 18:29:18,586 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:29:18,586 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:29:20,713 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:20,715 - INFO - INFO: Executing: pip show build
2025-07-27 18:29:22,170 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:29:22,171 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:22,171 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:29:22,172 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:29:22,172 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:29:24,140 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:24,140 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:29:25,460 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:29:25,460 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:25,461 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:29:25,461 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:29:25,461 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:29:27,587 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:27,588 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:29:29,049 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:29:29,050 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:29,050 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:29:29,050 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:29:29,050 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:29:29,051 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:29:31,240 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:31,240 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:29:32,721 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:29:32,722 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:32,722 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:29:32,722 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:29:32,722 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:29:34,756 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:34,756 - INFO - INFO: Executing: pip show twine
2025-07-27 18:29:36,123 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:29:36,123 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:36,124 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:29:36,124 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:29:36,124 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:29:36,124 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:29:36,125 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.87s)
================================================================================
2025-07-27 18:29:36,125 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:29:36,378 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.08s)
================================================================================
2025-07-27 18:29:36,378 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:29:36,378 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:29:36,378 - INFO - INFO: Python: 3.10.8
2025-07-27 18:29:36,378 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:29:36,378 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.08s)
================================================================================
2025-07-27 18:29:36,378 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:29:36,378 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:29:36,379 - INFO - INFO: Architecture: AMD64
2025-07-27 18:29:36,379 - INFO - SUCCESS: All required files present
2025-07-27 18:29:36,379 - INFO - SUCCESS: Core module directory found
2025-07-27 18:29:36,380 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:29:36,380 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:29:36,380 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.08s)
================================================================================
2025-07-27 18:29:36,380 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:29:36,384 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:29:36,404 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:29:36,404 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:29:36,404 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:29:36,404 - INFO - INFO: Setting up build directories
2025-07-27 18:29:36,404 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:29:36,404 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:29:36,404 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:29:36,404 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.11s)
================================================================================
2025-07-27 18:29:36,404 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:29:39,156 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:29:39,157 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:39,157 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:29:39,157 - INFO - INFO: Installing core dependencies
2025-07-27 18:29:39,157 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:29:39,157 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:29:41,332 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:41,333 - INFO - INFO: Executing: pip show build
2025-07-27 18:29:42,854 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:29:42,855 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:42,855 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:29:42,855 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:29:42,855 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:29:45,065 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:45,065 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:29:46,595 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:29:46,595 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:46,595 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:29:46,595 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:29:46,595 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:29:48,721 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:48,721 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:29:50,088 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:29:50,089 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:50,089 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:29:50,089 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:29:50,089 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:29:50,089 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:29:52,068 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:52,068 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:29:53,514 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:29:53,514 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:53,514 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:29:53,514 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:29:53,514 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:29:55,612 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:55,612 - INFO - INFO: Executing: pip show twine
2025-07-27 18:29:57,212 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:29:57,213 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:29:57,213 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:29:57,213 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:29:57,213 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:29:57,213 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:29:57,213 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.92s)
================================================================================
2025-07-27 18:29:57,213 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:29:57,446 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.07s)
================================================================================
2025-07-27 18:29:57,446 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:29:57,446 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:29:57,446 - INFO - INFO: Python: 3.10.8
2025-07-27 18:29:57,446 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:29:57,446 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.07s)
================================================================================
2025-07-27 18:29:57,446 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:29:57,446 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:29:57,446 - INFO - INFO: Architecture: AMD64
2025-07-27 18:29:57,447 - INFO - SUCCESS: All required files present
2025-07-27 18:29:57,447 - INFO - SUCCESS: Core module directory found
2025-07-27 18:29:57,447 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:29:57,447 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:29:57,447 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.07s)
================================================================================
2025-07-27 18:29:57,448 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:29:57,451 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:29:57,468 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:29:57,468 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:29:57,468 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:29:57,468 - INFO - INFO: Setting up build directories
2025-07-27 18:29:57,469 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:29:57,469 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:29:57,469 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:29:57,469 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.10s)
================================================================================
2025-07-27 18:29:57,469 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:30:00,249 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:30:00,249 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:00,249 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:30:00,249 - INFO - INFO: Installing core dependencies
2025-07-27 18:30:00,249 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:30:00,249 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:30:02,328 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:02,328 - INFO - INFO: Executing: pip show build
2025-07-27 18:30:03,897 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:30:03,897 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:03,897 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:30:03,897 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:30:03,897 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:30:05,969 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:05,970 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:30:07,517 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:30:07,517 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:07,517 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:30:07,517 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:30:07,517 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:30:09,820 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:09,820 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:30:11,212 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:30:11,213 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:11,213 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:30:11,213 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:30:11,213 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:30:11,213 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:30:13,264 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:13,264 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:30:14,712 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:30:14,713 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:14,713 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:30:14,713 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:30:14,713 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:30:16,940 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:16,940 - INFO - INFO: Executing: pip show twine
2025-07-27 18:30:18,539 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:30:18,540 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:18,540 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:30:18,540 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:30:18,540 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:30:18,540 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:30:18,540 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.17s)
================================================================================
2025-07-27 18:30:18,540 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:30:18,778 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.06s)
================================================================================
2025-07-27 18:30:18,778 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:30:18,778 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:30:18,778 - INFO - INFO: Python: 3.10.8
2025-07-27 18:30:18,778 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:30:18,778 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.06s)
================================================================================
2025-07-27 18:30:18,778 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:30:18,778 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:30:18,778 - INFO - INFO: Architecture: AMD64
2025-07-27 18:30:18,780 - INFO - SUCCESS: All required files present
2025-07-27 18:30:18,781 - INFO - SUCCESS: Core module directory found
2025-07-27 18:30:18,781 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:30:18,781 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:30:18,781 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.07s)
================================================================================
2025-07-27 18:30:18,782 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:30:18,786 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:30:18,817 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:30:18,817 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:30:18,817 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:30:18,817 - INFO - INFO: Setting up build directories
2025-07-27 18:30:18,818 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:30:18,818 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:30:18,818 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:30:18,818 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.10s)
================================================================================
2025-07-27 18:30:18,818 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:30:21,699 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:30:21,699 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:21,699 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:30:21,699 - INFO - INFO: Installing core dependencies
2025-07-27 18:30:21,699 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:30:21,699 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:30:23,658 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:23,659 - INFO - INFO: Executing: pip show build
2025-07-27 18:30:25,037 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:30:25,038 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:25,038 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:30:25,038 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:30:25,038 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:30:27,447 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:27,447 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:30:29,054 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:30:29,054 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:29,054 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:30:29,054 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:30:29,054 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:30:31,185 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:31,185 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:30:32,527 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:30:32,527 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:32,527 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:30:32,527 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:30:32,527 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:30:32,527 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:30:34,706 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:34,706 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:30:36,176 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:30:36,177 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:36,177 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:30:36,177 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:30:36,177 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:30:38,263 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:38,263 - INFO - INFO: Executing: pip show twine
2025-07-27 18:30:39,763 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:30:39,763 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:39,763 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:30:39,764 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:30:39,764 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:30:39,764 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:30:39,764 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.05s)
================================================================================
2025-07-27 18:30:39,764 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:30:40,245 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.21s)
================================================================================
2025-07-27 18:30:40,245 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:30:40,246 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:30:40,246 - INFO - INFO: Python: 3.10.8
2025-07-27 18:30:40,246 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:30:40,246 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.21s)
================================================================================
2025-07-27 18:30:40,246 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:30:40,246 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:30:40,246 - INFO - INFO: Architecture: AMD64
2025-07-27 18:30:40,248 - INFO - SUCCESS: All required files present
2025-07-27 18:30:40,248 - INFO - SUCCESS: Core module directory found
2025-07-27 18:30:40,248 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:30:40,248 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:30:40,248 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.21s)
================================================================================
2025-07-27 18:30:40,249 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:30:40,263 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:30:40,331 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:30:40,331 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:30:40,331 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:30:40,331 - INFO - INFO: Setting up build directories
2025-07-27 18:30:40,332 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:30:40,332 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:30:40,332 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:30:40,332 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.29s)
================================================================================
2025-07-27 18:30:40,332 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:30:46,098 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:30:46,098 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:46,098 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:30:46,098 - INFO - INFO: Installing core dependencies
2025-07-27 18:30:46,098 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:30:46,098 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:30:48,999 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:49,000 - INFO - INFO: Executing: pip show build
2025-07-27 18:30:50,794 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:30:50,794 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:50,794 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:30:50,794 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:30:50,794 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:30:53,260 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:53,260 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:30:55,006 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:30:55,007 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:55,007 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:30:55,007 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:30:55,008 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:30:57,677 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:57,677 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:30:59,331 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:30:59,331 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:30:59,332 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:30:59,332 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:30:59,332 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:30:59,332 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:31:01,471 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:01,471 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:31:02,914 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:31:02,914 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:02,914 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:31:02,914 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:31:02,914 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:31:05,034 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:05,034 - INFO - INFO: Executing: pip show twine
2025-07-27 18:31:06,527 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:31:06,527 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:06,527 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:31:06,527 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:31:06,527 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:31:06,528 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:31:06,528 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 26.49s)
================================================================================
2025-07-27 18:31:06,528 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:31:06,725 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:31:06,725 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:31:06,725 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:31:06,725 - INFO - INFO: Python: 3.10.8
2025-07-27 18:31:06,725 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:31:06,725 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:31:06,725 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:31:06,725 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:31:06,725 - INFO - INFO: Architecture: AMD64
2025-07-27 18:31:06,726 - INFO - SUCCESS: All required files present
2025-07-27 18:31:06,726 - INFO - SUCCESS: Core module directory found
2025-07-27 18:31:06,726 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:31:06,726 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:31:06,726 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:31:06,727 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:31:06,730 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:31:06,761 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:31:06,761 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:31:06,761 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:31:06,761 - INFO - INFO: Setting up build directories
2025-07-27 18:31:06,762 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:31:06,762 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:31:06,762 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:31:06,762 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.08s)
================================================================================
2025-07-27 18:31:06,762 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:31:09,769 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:31:09,770 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:09,770 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:31:09,770 - INFO - INFO: Installing core dependencies
2025-07-27 18:31:09,770 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:31:09,770 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:31:11,805 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:11,805 - INFO - INFO: Executing: pip show build
2025-07-27 18:31:13,187 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:31:13,188 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:13,188 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:31:13,188 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:31:13,188 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:31:15,308 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:15,308 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:31:16,891 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:31:16,891 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:16,891 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:31:16,891 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:31:16,891 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:31:19,096 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:19,096 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:31:20,603 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:31:20,605 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:20,605 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:31:20,605 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:31:20,605 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:31:20,605 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:31:22,786 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:22,786 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:31:24,296 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:31:24,296 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:24,296 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:31:24,296 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:31:24,296 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:31:26,378 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:26,379 - INFO - INFO: Executing: pip show twine
2025-07-27 18:31:28,075 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:31:28,075 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:28,075 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:31:28,075 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:31:28,075 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:31:28,075 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:31:28,075 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.40s)
================================================================================
2025-07-27 18:31:28,075 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:31:28,297 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.06s)
================================================================================
2025-07-27 18:31:28,297 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:31:28,297 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:31:28,297 - INFO - INFO: Python: 3.10.8
2025-07-27 18:31:28,297 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:31:28,297 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.06s)
================================================================================
2025-07-27 18:31:28,297 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:31:28,297 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:31:28,297 - INFO - INFO: Architecture: AMD64
2025-07-27 18:31:28,298 - INFO - SUCCESS: All required files present
2025-07-27 18:31:28,298 - INFO - SUCCESS: Core module directory found
2025-07-27 18:31:28,298 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:31:28,298 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:31:28,298 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.06s)
================================================================================
2025-07-27 18:31:28,299 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:31:28,304 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:31:28,331 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:31:28,331 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:31:28,331 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:31:28,331 - INFO - INFO: Setting up build directories
2025-07-27 18:31:28,332 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:31:28,332 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:31:28,332 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:31:28,332 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.09s)
================================================================================
2025-07-27 18:31:28,332 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:31:31,131 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:31:31,131 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:31,131 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:31:31,131 - INFO - INFO: Installing core dependencies
2025-07-27 18:31:31,131 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:31:31,131 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:31:33,234 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:33,234 - INFO - INFO: Executing: pip show build
2025-07-27 18:31:34,668 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:31:34,668 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:34,668 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:31:34,668 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:31:34,668 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:31:36,781 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:36,781 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:31:38,183 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:31:38,184 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:38,184 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:31:38,184 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:31:38,184 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:31:40,380 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:40,381 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:31:41,769 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:31:41,769 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:41,769 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:31:41,769 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:31:41,769 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:31:41,769 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:31:43,920 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:43,920 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:31:45,442 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:31:45,442 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:45,442 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:31:45,442 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:31:45,444 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:31:47,618 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:47,618 - INFO - INFO: Executing: pip show twine
2025-07-27 18:31:49,095 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:31:49,095 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:49,095 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:31:49,095 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:31:49,095 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:31:49,095 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:31:49,095 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.86s)
================================================================================
2025-07-27 18:31:49,096 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:31:49,310 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:31:49,311 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:31:49,312 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:31:49,312 - INFO - INFO: Python: 3.10.8
2025-07-27 18:31:49,312 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:31:49,312 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:31:49,312 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:31:49,312 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:31:49,312 - INFO - INFO: Architecture: AMD64
2025-07-27 18:31:49,313 - INFO - SUCCESS: All required files present
2025-07-27 18:31:49,313 - INFO - SUCCESS: Core module directory found
2025-07-27 18:31:49,313 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:31:49,313 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:31:49,313 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:31:49,313 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:31:49,317 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:31:49,335 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:31:49,335 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:31:49,335 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:31:49,335 - INFO - INFO: Setting up build directories
2025-07-27 18:31:49,336 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:31:49,336 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:31:49,336 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:31:49,336 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.08s)
================================================================================
2025-07-27 18:31:49,336 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:31:52,241 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:31:52,241 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:52,241 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:31:52,241 - INFO - INFO: Installing core dependencies
2025-07-27 18:31:52,241 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:31:52,241 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:31:54,279 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:54,279 - INFO - INFO: Executing: pip show build
2025-07-27 18:31:55,834 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:31:55,834 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:55,834 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:31:55,834 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:31:55,834 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:31:57,886 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:57,886 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:31:59,340 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:31:59,340 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:31:59,341 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:31:59,341 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:31:59,341 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:32:01,414 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:01,414 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:32:02,956 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:32:02,956 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:02,956 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:32:02,956 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:32:02,957 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:32:02,957 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:32:05,198 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:05,199 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:32:06,716 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:32:06,716 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:06,716 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:32:06,716 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:32:06,716 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:32:08,834 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:08,834 - INFO - INFO: Executing: pip show twine
2025-07-27 18:32:10,365 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:32:10,365 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:10,365 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:32:10,365 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:32:10,365 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:32:10,365 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:32:10,365 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.11s)
================================================================================
2025-07-27 18:32:10,365 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:32:10,604 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.07s)
================================================================================
2025-07-27 18:32:10,604 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:32:10,605 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:32:10,605 - INFO - INFO: Python: 3.10.8
2025-07-27 18:32:10,605 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:32:10,605 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.07s)
================================================================================
2025-07-27 18:32:10,605 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:32:10,605 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:32:10,605 - INFO - INFO: Architecture: AMD64
2025-07-27 18:32:10,606 - INFO - SUCCESS: All required files present
2025-07-27 18:32:10,607 - INFO - SUCCESS: Core module directory found
2025-07-27 18:32:10,607 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:32:10,607 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:32:10,607 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.07s)
================================================================================
2025-07-27 18:32:10,608 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:32:10,615 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:32:10,642 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:32:10,643 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:32:10,643 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:32:10,643 - INFO - INFO: Setting up build directories
2025-07-27 18:32:10,644 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:32:10,644 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:32:10,644 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:32:10,644 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.10s)
================================================================================
2025-07-27 18:32:10,644 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:32:13,437 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:32:13,437 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:13,437 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:32:13,437 - INFO - INFO: Installing core dependencies
2025-07-27 18:32:13,437 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:32:13,437 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:32:15,688 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:15,688 - INFO - INFO: Executing: pip show build
2025-07-27 18:32:17,174 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:32:17,174 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:17,174 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:32:17,174 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:32:17,174 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:32:19,160 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:19,160 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:32:20,696 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:32:20,696 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:20,696 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:32:20,696 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:32:20,696 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:32:23,134 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:23,134 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:32:24,593 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:32:24,593 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:24,593 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:32:24,593 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:32:24,593 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:32:24,593 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:32:27,086 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:27,087 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:32:28,708 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:32:28,708 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:28,708 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:32:28,708 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:32:28,708 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:32:30,979 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:30,979 - INFO - INFO: Executing: pip show twine
2025-07-27 18:32:32,526 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:32:32,526 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:32,527 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:32:32,527 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:32:32,527 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:32:32,527 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:32:32,527 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.99s)
================================================================================
2025-07-27 18:32:32,527 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:32:32,738 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:32:32,738 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:32:32,738 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:32:32,738 - INFO - INFO: Python: 3.10.8
2025-07-27 18:32:32,738 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:32:32,738 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:32:32,738 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:32:32,738 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:32:32,738 - INFO - INFO: Architecture: AMD64
2025-07-27 18:32:32,739 - INFO - SUCCESS: All required files present
2025-07-27 18:32:32,739 - INFO - SUCCESS: Core module directory found
2025-07-27 18:32:32,739 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:32:32,739 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:32:32,739 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:32:32,740 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:32:32,744 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:32:32,760 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:32:32,760 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:32:32,760 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:32:32,760 - INFO - INFO: Setting up build directories
2025-07-27 18:32:32,761 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:32:32,761 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:32:32,761 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:32:32,761 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:32:32,761 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:32:35,544 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:32:35,544 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:35,544 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:32:35,544 - INFO - INFO: Installing core dependencies
2025-07-27 18:32:35,544 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:32:35,545 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:32:37,781 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:37,782 - INFO - INFO: Executing: pip show build
2025-07-27 18:32:39,200 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:32:39,200 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:39,200 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:32:39,200 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:32:39,200 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:32:41,210 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:41,210 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:32:42,652 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:32:42,652 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:42,652 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:32:42,652 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:32:42,652 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:32:44,837 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:44,837 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:32:46,474 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:32:46,474 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:46,474 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:32:46,474 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:32:46,474 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:32:46,474 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:32:48,827 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:48,827 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:32:50,208 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:32:50,208 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:50,208 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:32:50,208 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:32:50,208 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:32:52,304 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:52,304 - INFO - INFO: Executing: pip show twine
2025-07-27 18:32:53,666 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:32:53,666 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:53,666 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:32:53,666 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:32:53,666 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:32:53,666 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:32:53,666 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.98s)
================================================================================
2025-07-27 18:32:53,666 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:32:53,897 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:32:53,897 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:32:53,897 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:32:53,897 - INFO - INFO: Python: 3.10.8
2025-07-27 18:32:53,897 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:32:53,897 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:32:53,897 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:32:53,897 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:32:53,897 - INFO - INFO: Architecture: AMD64
2025-07-27 18:32:53,897 - INFO - SUCCESS: All required files present
2025-07-27 18:32:53,897 - INFO - SUCCESS: Core module directory found
2025-07-27 18:32:53,897 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:32:53,897 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:32:53,897 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:32:53,898 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:32:53,901 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:32:53,927 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:32:53,927 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:32:53,927 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:32:53,927 - INFO - INFO: Setting up build directories
2025-07-27 18:32:53,928 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:32:53,928 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:32:53,928 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:32:53,928 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.08s)
================================================================================
2025-07-27 18:32:53,928 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:32:56,623 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:32:56,623 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:56,623 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:32:56,623 - INFO - INFO: Installing core dependencies
2025-07-27 18:32:56,623 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:32:56,623 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:32:58,750 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:32:58,750 - INFO - INFO: Executing: pip show build
2025-07-27 18:33:00,271 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:33:00,272 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:00,272 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:33:00,272 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:33:00,272 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:33:02,245 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:02,245 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:33:03,705 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:33:03,705 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:03,705 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:33:03,705 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:33:03,705 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:33:05,871 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:05,871 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:33:07,194 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:33:07,194 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:07,194 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:33:07,196 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:33:07,196 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:33:07,196 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:33:09,203 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:09,204 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:33:10,687 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:33:10,688 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:10,688 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:33:10,688 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:33:10,688 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:33:12,798 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:12,799 - INFO - INFO: Executing: pip show twine
2025-07-27 18:33:14,236 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:33:14,236 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:14,236 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:33:14,236 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:33:14,237 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:33:14,237 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:33:14,237 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.39s)
================================================================================
2025-07-27 18:33:14,237 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:33:14,443 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:33:14,444 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:33:14,444 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:33:14,444 - INFO - INFO: Python: 3.10.8
2025-07-27 18:33:14,444 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:33:14,444 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:33:14,444 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:33:14,444 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:33:14,444 - INFO - INFO: Architecture: AMD64
2025-07-27 18:33:14,446 - INFO - SUCCESS: All required files present
2025-07-27 18:33:14,446 - INFO - SUCCESS: Core module directory found
2025-07-27 18:33:14,446 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:33:14,446 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:33:14,446 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:33:14,447 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:33:14,450 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:33:14,465 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:33:14,465 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:33:14,465 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:33:14,465 - INFO - INFO: Setting up build directories
2025-07-27 18:33:14,465 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:33:14,465 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:33:14,466 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:33:14,466 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:33:14,466 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:33:17,251 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:33:17,252 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:17,252 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:33:17,252 - INFO - INFO: Installing core dependencies
2025-07-27 18:33:17,252 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:33:17,252 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:33:19,320 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:19,321 - INFO - INFO: Executing: pip show build
2025-07-27 18:33:20,839 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:33:20,839 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:20,839 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:33:20,839 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:33:20,839 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:33:22,795 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:22,795 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:33:24,216 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:33:24,216 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:24,216 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:33:24,216 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:33:24,216 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:33:26,384 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:26,384 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:33:27,712 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:33:27,712 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:27,712 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:33:27,712 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:33:27,712 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:33:27,712 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:33:29,748 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:29,748 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:33:31,181 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:33:31,181 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:31,181 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:33:31,181 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:33:31,181 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:33:33,150 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:33,150 - INFO - INFO: Executing: pip show twine
2025-07-27 18:33:34,499 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:33:34,499 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:34,499 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:33:34,499 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:33:34,499 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:33:34,499 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:33:34,499 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.10s)
================================================================================
2025-07-27 18:33:34,499 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:33:34,704 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:33:34,704 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:33:34,704 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:33:34,705 - INFO - INFO: Python: 3.10.8
2025-07-27 18:33:34,705 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:33:34,705 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:33:34,705 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:33:34,705 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:33:34,705 - INFO - INFO: Architecture: AMD64
2025-07-27 18:33:34,706 - INFO - SUCCESS: All required files present
2025-07-27 18:33:34,706 - INFO - SUCCESS: Core module directory found
2025-07-27 18:33:34,706 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:33:34,706 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:33:34,706 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:33:34,707 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:33:34,711 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:33:34,730 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:33:34,730 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:33:34,732 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:33:34,732 - INFO - INFO: Setting up build directories
2025-07-27 18:33:34,732 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:33:34,732 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:33:34,732 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:33:34,732 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:33:34,732 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:33:37,525 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:33:37,525 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:37,525 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:33:37,525 - INFO - INFO: Installing core dependencies
2025-07-27 18:33:37,525 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:33:37,525 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:33:39,526 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:39,526 - INFO - INFO: Executing: pip show build
2025-07-27 18:33:40,937 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:33:40,938 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:40,938 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:33:40,938 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:33:40,938 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:33:43,016 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:43,016 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:33:44,405 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:33:44,405 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:44,405 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:33:44,405 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:33:44,405 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:33:46,695 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:46,696 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:33:48,096 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:33:48,096 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:48,097 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:33:48,097 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:33:48,097 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:33:48,097 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:33:50,154 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:50,154 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:33:51,551 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:33:51,551 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:51,551 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:33:51,551 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:33:51,551 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:33:53,625 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:53,625 - INFO - INFO: Executing: pip show twine
2025-07-27 18:33:55,024 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:33:55,024 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:55,024 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:33:55,024 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:33:55,024 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:33:55,025 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:33:55,025 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.37s)
================================================================================
2025-07-27 18:33:55,025 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:33:55,226 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.04s)
================================================================================
2025-07-27 18:33:55,226 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:33:55,226 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:33:55,226 - INFO - INFO: Python: 3.10.8
2025-07-27 18:33:55,226 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:33:55,226 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.04s)
================================================================================
2025-07-27 18:33:55,226 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:33:55,226 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:33:55,227 - INFO - INFO: Architecture: AMD64
2025-07-27 18:33:55,227 - INFO - SUCCESS: All required files present
2025-07-27 18:33:55,227 - INFO - SUCCESS: Core module directory found
2025-07-27 18:33:55,227 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:33:55,227 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:33:55,227 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:33:55,228 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:33:55,230 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:33:55,245 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:33:55,245 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:33:55,245 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:33:55,245 - INFO - INFO: Setting up build directories
2025-07-27 18:33:55,246 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:33:55,246 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:33:55,246 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:33:55,246 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.06s)
================================================================================
2025-07-27 18:33:55,246 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:33:57,949 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:33:57,949 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:57,949 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:33:57,949 - INFO - INFO: Installing core dependencies
2025-07-27 18:33:57,949 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:33:57,949 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:33:59,931 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:33:59,932 - INFO - INFO: Executing: pip show build
2025-07-27 18:34:01,343 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:34:01,343 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:01,343 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:34:01,343 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:34:01,343 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:34:03,392 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:03,392 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:34:04,679 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:34:04,680 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:04,680 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:34:04,680 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:34:04,680 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:34:06,792 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:06,793 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:34:08,267 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:34:08,267 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:08,268 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:34:08,268 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:34:08,268 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:34:08,268 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:34:10,241 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:10,241 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:34:11,684 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:34:11,685 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:11,685 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:34:11,685 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:34:11,685 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:34:13,751 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:13,752 - INFO - INFO: Executing: pip show twine
2025-07-27 18:34:15,039 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:34:15,039 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:15,039 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:34:15,039 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:34:15,039 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:34:15,039 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:34:15,039 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 19.86s)
================================================================================
2025-07-27 18:34:15,039 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:34:15,238 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:15,238 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:34:15,238 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:34:15,238 - INFO - INFO: Python: 3.10.8
2025-07-27 18:34:15,238 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:34:15,238 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:15,238 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:34:15,238 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:34:15,238 - INFO - INFO: Architecture: AMD64
2025-07-27 18:34:15,239 - INFO - SUCCESS: All required files present
2025-07-27 18:34:15,239 - INFO - SUCCESS: Core module directory found
2025-07-27 18:34:15,239 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:34:15,239 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:34:15,239 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:15,240 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:34:15,243 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:34:15,258 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:34:15,258 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:34:15,258 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:34:15,258 - INFO - INFO: Setting up build directories
2025-07-27 18:34:15,259 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:34:15,259 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:34:15,259 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:34:15,259 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:34:15,259 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:34:18,172 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:34:18,173 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:18,173 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:34:18,173 - INFO - INFO: Installing core dependencies
2025-07-27 18:34:18,173 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:34:18,173 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:34:20,231 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:20,231 - INFO - INFO: Executing: pip show build
2025-07-27 18:34:21,583 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:34:21,583 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:21,583 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:34:21,583 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:34:21,583 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:34:23,812 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:23,813 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:34:25,158 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:34:25,158 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:25,159 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:34:25,159 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:34:25,159 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:34:27,180 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:27,180 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:34:28,570 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:34:28,570 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:28,570 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:34:28,570 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:34:28,570 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:34:28,570 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:34:30,592 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:30,592 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:34:32,077 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:34:32,077 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:32,077 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:34:32,078 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:34:32,078 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:34:34,407 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:34,407 - INFO - INFO: Executing: pip show twine
2025-07-27 18:34:35,735 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:34:35,736 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:35,736 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:34:35,736 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:34:35,736 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:34:35,736 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:34:35,736 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.54s)
================================================================================
2025-07-27 18:34:35,736 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:34:35,944 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:35,944 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:34:35,944 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:34:35,944 - INFO - INFO: Python: 3.10.8
2025-07-27 18:34:35,944 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:34:35,944 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:35,944 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:34:35,944 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:34:35,944 - INFO - INFO: Architecture: AMD64
2025-07-27 18:34:35,945 - INFO - SUCCESS: All required files present
2025-07-27 18:34:35,945 - INFO - SUCCESS: Core module directory found
2025-07-27 18:34:35,945 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:34:35,945 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:34:35,945 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:35,946 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:34:35,949 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:34:35,967 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:34:35,968 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:34:35,968 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:34:35,968 - INFO - INFO: Setting up build directories
2025-07-27 18:34:35,968 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:34:35,968 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:34:35,968 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:34:35,968 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:34:35,968 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:34:38,822 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:34:38,822 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:38,822 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:34:38,822 - INFO - INFO: Installing core dependencies
2025-07-27 18:34:38,823 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:34:38,823 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:34:40,982 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:40,983 - INFO - INFO: Executing: pip show build
2025-07-27 18:34:42,359 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:34:42,359 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:42,359 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:34:42,359 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:34:42,359 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:34:44,521 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:44,521 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:34:45,938 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:34:45,938 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:45,938 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:34:45,938 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:34:45,938 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:34:48,065 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:48,065 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:34:49,535 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:34:49,535 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:49,535 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:34:49,535 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:34:49,535 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:34:49,535 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:34:51,637 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:51,637 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:34:52,963 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:34:52,963 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:52,963 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:34:52,963 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:34:52,963 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:34:54,964 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:54,964 - INFO - INFO: Executing: pip show twine
2025-07-27 18:34:56,371 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:34:56,371 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:56,371 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:34:56,372 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:34:56,372 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:34:56,372 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:34:56,372 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.47s)
================================================================================
2025-07-27 18:34:56,372 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:34:56,611 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:56,611 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:34:56,611 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:34:56,611 - INFO - INFO: Python: 3.10.8
2025-07-27 18:34:56,611 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:34:56,611 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:56,611 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:34:56,611 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:34:56,611 - INFO - INFO: Architecture: AMD64
2025-07-27 18:34:56,612 - INFO - SUCCESS: All required files present
2025-07-27 18:34:56,612 - INFO - SUCCESS: Core module directory found
2025-07-27 18:34:56,613 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:34:56,613 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:34:56,613 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:34:56,614 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:34:56,617 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:34:56,636 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:34:56,636 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:34:56,636 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:34:56,636 - INFO - INFO: Setting up build directories
2025-07-27 18:34:56,637 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:34:56,637 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:34:56,637 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:34:56,637 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.08s)
================================================================================
2025-07-27 18:34:56,637 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:34:59,499 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:34:59,499 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:34:59,499 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:34:59,499 - INFO - INFO: Installing core dependencies
2025-07-27 18:34:59,499 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:34:59,499 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:35:01,679 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:01,680 - INFO - INFO: Executing: pip show build
2025-07-27 18:35:02,984 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:35:02,984 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:02,984 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:35:02,984 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:35:02,984 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:35:05,217 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:05,217 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:35:06,784 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:35:06,784 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:06,784 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:35:06,784 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:35:06,784 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:35:08,974 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:08,975 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:35:10,460 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:35:10,460 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:10,460 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:35:10,460 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:35:10,460 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:35:10,460 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:35:12,651 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:12,651 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:35:13,977 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:35:13,978 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:13,978 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:35:13,978 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:35:13,978 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:35:16,126 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:16,126 - INFO - INFO: Executing: pip show twine
2025-07-27 18:35:17,521 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:35:17,521 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:17,521 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:35:17,522 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:35:17,522 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:35:17,522 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:35:17,522 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.96s)
================================================================================
2025-07-27 18:35:17,522 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:35:17,725 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:35:17,725 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:35:17,725 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:35:17,725 - INFO - INFO: Python: 3.10.8
2025-07-27 18:35:17,725 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:35:17,725 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:35:17,725 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:35:17,725 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:35:17,725 - INFO - INFO: Architecture: AMD64
2025-07-27 18:35:17,726 - INFO - SUCCESS: All required files present
2025-07-27 18:35:17,726 - INFO - SUCCESS: Core module directory found
2025-07-27 18:35:17,726 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:35:17,726 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:35:17,726 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:35:17,727 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:35:17,730 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:35:17,749 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:35:17,749 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:35:17,749 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:35:17,749 - INFO - INFO: Setting up build directories
2025-07-27 18:35:17,750 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:35:17,750 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:35:17,750 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:35:17,750 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:35:17,750 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:35:20,539 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:35:20,539 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:20,539 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:35:20,539 - INFO - INFO: Installing core dependencies
2025-07-27 18:35:20,539 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:35:20,539 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:35:22,875 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:22,875 - INFO - INFO: Executing: pip show build
2025-07-27 18:35:24,229 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:35:24,229 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:24,229 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:35:24,229 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:35:24,229 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:35:26,437 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:26,437 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:35:27,981 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:35:27,981 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:27,981 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:35:27,981 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:35:27,981 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:35:30,128 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:30,128 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:35:31,563 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:35:31,563 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:31,564 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:35:31,564 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:35:31,564 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:35:31,564 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:35:33,965 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:33,965 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:35:35,380 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:35:35,380 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:35,381 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:35:35,381 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:35:35,381 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:35:37,507 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:37,508 - INFO - INFO: Executing: pip show twine
2025-07-27 18:35:39,106 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:35:39,106 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:39,106 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:35:39,106 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:35:39,106 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:35:39,106 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:35:39,106 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.43s)
================================================================================
2025-07-27 18:35:39,106 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:35:39,357 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.04s)
================================================================================
2025-07-27 18:35:39,358 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:35:39,358 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:35:39,358 - INFO - INFO: Python: 3.10.8
2025-07-27 18:35:39,358 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:35:39,358 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:35:39,358 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:35:39,358 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:35:39,358 - INFO - INFO: Architecture: AMD64
2025-07-27 18:35:39,358 - INFO - SUCCESS: All required files present
2025-07-27 18:35:39,359 - INFO - SUCCESS: Core module directory found
2025-07-27 18:35:39,359 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:35:39,359 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:35:39,359 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.05s)
================================================================================
2025-07-27 18:35:39,359 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:35:39,362 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:35:39,378 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:35:39,379 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:35:39,379 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:35:39,379 - INFO - INFO: Setting up build directories
2025-07-27 18:35:39,379 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:35:39,379 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:35:39,379 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:35:39,379 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:35:39,379 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:35:42,106 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:35:42,106 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:42,106 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:35:42,106 - INFO - INFO: Installing core dependencies
2025-07-27 18:35:42,106 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:35:42,106 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:35:44,301 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:44,301 - INFO - INFO: Executing: pip show build
2025-07-27 18:35:45,870 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:35:45,870 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:45,870 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:35:45,870 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:35:45,870 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:35:48,287 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:48,288 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:35:49,734 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:35:49,734 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:49,734 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:35:49,734 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:35:49,734 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:35:51,766 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:51,766 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:35:53,113 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:35:53,113 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:53,113 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:35:53,114 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:35:53,114 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:35:53,114 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:35:55,234 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:55,234 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:35:56,877 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:35:56,877 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:56,877 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:35:56,877 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:35:56,877 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:35:59,326 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:35:59,326 - INFO - INFO: Executing: pip show twine
2025-07-27 18:36:00,809 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:36:00,809 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:00,809 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:36:00,809 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:36:00,809 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:36:00,809 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:36:00,809 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 21.50s)
================================================================================
2025-07-27 18:36:00,809 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:36:01,029 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.04s)
================================================================================
2025-07-27 18:36:01,029 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:36:01,029 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:36:01,030 - INFO - INFO: Python: 3.10.8
2025-07-27 18:36:01,030 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:36:01,030 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.04s)
================================================================================
2025-07-27 18:36:01,030 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:36:01,030 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:36:01,030 - INFO - INFO: Architecture: AMD64
2025-07-27 18:36:01,031 - INFO - SUCCESS: All required files present
2025-07-27 18:36:01,031 - INFO - SUCCESS: Core module directory found
2025-07-27 18:36:01,031 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:36:01,031 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:36:01,031 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.04s)
================================================================================
2025-07-27 18:36:01,032 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:36:01,035 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:36:01,053 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:36:01,053 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:36:01,053 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:36:01,053 - INFO - INFO: Setting up build directories
2025-07-27 18:36:01,054 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:36:01,054 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:36:01,054 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:36:01,054 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.07s)
================================================================================
2025-07-27 18:36:01,054 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:36:04,007 - INFO - INFO: Output: Requirement already satisfied: pip in d:\software\python\python310\lib\site-packages (25.1.1)
2025-07-27 18:36:04,007 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:04,007 - INFO - SUCCESS: pip upgraded successfully
2025-07-27 18:36:04,007 - INFO - INFO: Installing core dependencies
2025-07-27 18:36:04,007 - INFO - INFO: Installing: build>=0.10.0
2025-07-27 18:36:04,007 - INFO - INFO: Executing: pip install build>=0.10.0
2025-07-27 18:36:06,104 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:06,104 - INFO - INFO: Executing: pip show build
2025-07-27 18:36:07,745 - INFO - INFO: Output: Name: build
Version: 1.2.2.post1
Summary: A simple, correct Python build frontend
Home-page: https://build.pypa.io
Author: 
Author-email: Filipe La??ns <<EMAIL>>, Bern??t G??bor <<EMAIL>>, layday <<EMAIL>>, Henry Schreiner <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: colorama, packaging, pyproject_hooks, tomli
Required-by:
2025-07-27 18:36:07,745 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:07,745 - INFO - SUCCESS: Installed build v1.2.2.post1
2025-07-27 18:36:07,745 - INFO - INFO: Installing: wheel>=0.40.0
2025-07-27 18:36:07,745 - INFO - INFO: Executing: pip install wheel>=0.40.0
2025-07-27 18:36:09,925 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:09,925 - INFO - INFO: Executing: pip show wheel
2025-07-27 18:36:11,361 - INFO - INFO: Output: Name: wheel
Version: 0.41.2
Summary: A built-package format for Python
Home-page: 
Author: 
Author-email: Daniel Holth <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: astunparse
2025-07-27 18:36:11,361 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:11,361 - INFO - SUCCESS: Installed wheel v0.41.2
2025-07-27 18:36:11,361 - INFO - INFO: Installing: setuptools>=68.0.0
2025-07-27 18:36:11,361 - INFO - INFO: Executing: pip install setuptools>=68.0.0
2025-07-27 18:36:13,422 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:13,422 - INFO - INFO: Executing: pip show setuptools
2025-07-27 18:36:14,864 - INFO - INFO: Output: Name: setuptools
Version: 63.2.0
Summary: Easily download, build, install, upgrade, and uninstall Python packages
Home-page: https://github.com/pypa/setuptools
Author: Python Packaging Authority
Author-email: <EMAIL>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: 
Required-by: pyinstaller, pyinstaller-hooks-contrib, tensorboard, tensorflow-intel, wandb
2025-07-27 18:36:14,864 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:14,865 - INFO - SUCCESS: Installed setuptools v63.2.0
2025-07-27 18:36:14,865 - INFO - INFO: Installing packaging dependencies
2025-07-27 18:36:14,865 - INFO - INFO: Installing: pyinstaller>=5.13.0
2025-07-27 18:36:14,865 - INFO - INFO: Executing: pip install pyinstaller>=5.13.0
2025-07-27 18:36:16,877 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:16,877 - INFO - INFO: Executing: pip show pyinstaller
2025-07-27 18:36:18,179 - INFO - INFO: Output: Name: pyinstaller
Version: 6.14.2
Summary: PyInstaller bundles a Python application and all its dependencies into a single package.
Home-page: https://www.pyinstaller.org/
Author: Hartmut Goebel, Giovanni Bajo, David Vierra, David Cortesi, Martin Zibricky
Author-email: 
License: GPLv2-or-later with a special exception which allows to use PyInstaller to build and distribute non-free programs (including commercial ones)
Location: d:\software\python\python310\lib\site-packages
Requires: altgraph, packaging, pefile, pyinstaller-hooks-contrib, pywin32-ctypes, setuptools
Required-by:
2025-07-27 18:36:18,179 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:18,179 - INFO - SUCCESS: Installed pyinstaller v6.14.2
2025-07-27 18:36:18,179 - INFO - INFO: Installing: twine>=4.0.0
2025-07-27 18:36:18,179 - INFO - INFO: Executing: pip install twine>=4.0.0
2025-07-27 18:36:20,235 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:20,235 - INFO - INFO: Executing: pip show twine
2025-07-27 18:36:21,868 - INFO - INFO: Output: Name: twine
Version: 6.1.0
Summary: Collection of utilities for publishing packages on PyPI
Home-page: https://twine.readthedocs.io/
Author: 
Author-email: Donald Stufft and individual contributors <<EMAIL>>
License: 
Location: d:\software\python\python310\lib\site-packages
Requires: id, keyring, packaging, readme-renderer, requests, requests-toolbelt, rfc3986, rich, urllib3
Required-by:
2025-07-27 18:36:21,868 - INFO - SUCCESS: Command completed (code: 0)
2025-07-27 18:36:21,868 - INFO - SUCCESS: Installed twine v6.1.0
2025-07-27 18:36:21,868 - INFO - SUCCESS: Dependencies installed: 5 packages
2025-07-27 18:36:21,868 - INFO - SUCCESS: Build step completed: Dependency Installation
2025-07-27 18:36:21,868 - INFO - INFO: Executing: Python Package Build
2025-07-27 18:36:21,868 - INFO - 
================================================================================
STEP 5: Building Python Packages (Elapsed: 20.88s)
================================================================================
2025-07-27 18:36:21,868 - INFO - INFO: Executing: python -m build --wheel --sdist --outdir dist
2025-07-27 18:36:22,106 - INFO - 
================================================================================
STEP 1: Starting AugmentCode-Free v1.0.6 Build Process (Elapsed: 0.05s)
================================================================================
2025-07-27 18:36:22,107 - INFO - INFO: Build configuration: {'python_min_version': (3, 7), 'build_timeout': 600, 'parallel_builds': True, 'compression_level': 9, 'create_checksums': True, 'validate_builds': True, 'cleanup_temp': True}
2025-07-27 18:36:22,107 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:36:22,107 - INFO - INFO: Python: 3.10.8
2025-07-27 18:36:22,107 - INFO - INFO: Executing: Environment Validation
2025-07-27 18:36:22,107 - INFO - 
================================================================================
STEP 2: Validating Build Environment (Elapsed: 0.05s)
================================================================================
2025-07-27 18:36:22,107 - INFO - SUCCESS: Python version: 3.10.8
2025-07-27 18:36:22,107 - INFO - INFO: Platform: Windows-10-10.0.22621-SP0
2025-07-27 18:36:22,107 - INFO - INFO: Architecture: AMD64
2025-07-27 18:36:22,107 - INFO - SUCCESS: All required files present
2025-07-27 18:36:22,109 - INFO - SUCCESS: Core module directory found
2025-07-27 18:36:22,109 - INFO - SUCCESS: Build step completed: Environment Validation
2025-07-27 18:36:22,109 - INFO - INFO: Executing: Artifact Cleanup
2025-07-27 18:36:22,109 - INFO - 
================================================================================
STEP 3: Cleaning Build Artifacts (Elapsed: 0.06s)
================================================================================
2025-07-27 18:36:22,109 - INFO - SUCCESS: Removed directory: dist
2025-07-27 18:36:22,112 - INFO - SUCCESS: Removed cache: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\__pycache__
2025-07-27 18:36:22,132 - INFO - SUCCESS: Cleanup completed: 2 items removed
2025-07-27 18:36:22,132 - INFO - SUCCESS: Build step completed: Artifact Cleanup
2025-07-27 18:36:22,132 - INFO - INFO: Executing: Directory Setup
2025-07-27 18:36:22,132 - INFO - INFO: Setting up build directories
2025-07-27 18:36:22,133 - INFO - SUCCESS: Build directory ready: E:\Data\Own\Entrepreneurship\aug-cursorpro\AugmentCode-Free\dist
2025-07-27 18:36:22,133 - INFO - SUCCESS: Build step completed: Directory Setup
2025-07-27 18:36:22,133 - INFO - INFO: Executing: Dependency Installation
2025-07-27 18:36:22,133 - INFO - 
================================================================================
STEP 4: Installing Build Dependencies (Elapsed: 0.08s)
================================================================================
2025-07-27 18:36:22,133 - INFO - INFO: Executing: python -m pip install --upgrade pip
2025-07-27 18:36:22,866 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,883 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,902 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,914 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,927 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,939 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,952 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,966 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,983 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:22,999 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,014 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,031 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,050 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,064 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,077 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,094 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,107 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,122 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,132 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,143 - ERROR - ERROR: Build interrupted by user
2025-07-27 18:36:23,152 - ERROR - ERROR: Build interrupted by user
